import gc
import psutil
import torch
import time
import threading
from typing import Dict, List, Optional, Any, Tuple, Callable
from dataclasses import dataclass, field
import warnings

from src.shared.logging.unified_logger import get_unified_logger


@dataclass
class ResourceLimits:
    """Resource limits configuration - optimized for high-performance systems"""
    max_system_memory_gb: float = 20.0  # Increased default for modern systems
    max_gpu_memory_gb: float = 6.8      # RTX 3070 optimized default
    max_cpu_percent: float = 80.0
    memory_warning_threshold: float = 0.80  # 80% usage warning (less conservative)
    memory_critical_threshold: float = 0.90  # 90% usage critical (allows higher utilization)
    cleanup_interval_seconds: float = 30.0
    
    # High usage memory release thresholds (70-80% utilization target)
    high_usage_memory_threshold: float = 0.70  # 70% usage triggers automatic cleanup
    high_usage_gpu_threshold: float = 0.70     # 70% GPU usage triggers cleanup
    aggressive_cleanup_threshold: float = 0.85  # 85% usage triggers aggressive cleanup
    memory_release_interval_seconds: float = 15.0  # More frequent memory release checks


@dataclass
class ResourceStatus:
    """Current resource status"""
    system_memory_used_gb: float
    system_memory_available_gb: float
    system_memory_percent: float
    gpu_memory_used_gb: float
    gpu_memory_available_gb: float
    gpu_memory_percent: float
    cpu_percent: float
    timestamp: float = field(default_factory=time.time)


class ResourceMonitor:
    """
    Real-time resource monitoring with alerts and automatic cleanup
    
    Continuously monitors system resources and provides alerts when
    thresholds are exceeded, with automatic cleanup capabilities.
    """
    
    def __init__(self, limits: ResourceLimits):
        self.limits = limits
        self.logger = get_unified_logger(f"{__name__}.ResourceMonitor")
        
        # Monitoring state
        self.monitoring_active = False
        self.monitor_thread = None
        self.resource_history: List[ResourceStatus] = []
        self.alert_callbacks: List[Callable] = []
        
        # Resource tracking
        self.peak_memory_usage = 0.0
        self.peak_gpu_usage = 0.0
        self.memory_warnings = 0
        self.memory_critical_events = 0
        
        # High usage memory release tracking
        self.memory_releases_performed = 0
        self.aggressive_cleanups_performed = 0
        self.last_memory_release = 0.0
        self.last_gpu_cleanup = 0.0
        self.cleanup_effectiveness_history = []
        
        self.logger.info("Resource monitor initialized")
    
    def get_current_status(self) -> ResourceStatus:
        """Get current resource status"""
        try:
            # System memory
            memory = psutil.virtual_memory()
            system_memory_used_gb = (memory.total - memory.available) / (1024**3)
            system_memory_available_gb = memory.available / (1024**3)
            system_memory_percent = memory.percent
            
            # GPU memory
            gpu_memory_used_gb = 0.0
            gpu_memory_available_gb = 0.0
            gpu_memory_percent = 0.0
            
            if torch.cuda.is_available():
                gpu_memory_used = torch.cuda.memory_allocated() / (1024**3)
                gpu_memory_reserved = torch.cuda.memory_reserved() / (1024**3)
                gpu_memory_total = torch.cuda.get_device_properties(0).total_memory / (1024**3)
                
                gpu_memory_used_gb = gpu_memory_reserved  # Use reserved as it's more accurate
                gpu_memory_available_gb = gpu_memory_total - gpu_memory_reserved
                gpu_memory_percent = (gpu_memory_reserved / gpu_memory_total) * 100
            
            # CPU usage
            cpu_percent = psutil.cpu_percent(interval=0.1)
            
            status = ResourceStatus(
                system_memory_used_gb=system_memory_used_gb,
                system_memory_available_gb=system_memory_available_gb,
                system_memory_percent=system_memory_percent,
                gpu_memory_used_gb=gpu_memory_used_gb,
                gpu_memory_available_gb=gpu_memory_available_gb,
                gpu_memory_percent=gpu_memory_percent,
                cpu_percent=cpu_percent
            )
            
            # Update peaks
            self.peak_memory_usage = max(self.peak_memory_usage, system_memory_used_gb)
            self.peak_gpu_usage = max(self.peak_gpu_usage, gpu_memory_used_gb)
            
            return status
            
        except Exception as e:
            self.logger.error(f"Failed to get resource status: {e}")
            return ResourceStatus(0, 0, 0, 0, 0, 0, 0)
    
    def check_resource_availability(self, required_memory_gb: float = 0.0, 
                                  required_gpu_memory_gb: float = 0.0) -> Tuple[bool, str]:
        """Check if required resources are available"""
        status = self.get_current_status()
        
        # Check system memory
        if required_memory_gb > 0:
            if status.system_memory_available_gb < required_memory_gb:
                return False, f"Insufficient system memory: need {required_memory_gb:.1f}GB, available {status.system_memory_available_gb:.1f}GB"
            
            # Check if this would exceed limits
            projected_usage = status.system_memory_used_gb + required_memory_gb
            if projected_usage > self.limits.max_system_memory_gb:
                return False, f"Would exceed memory limit: projected {projected_usage:.1f}GB > limit {self.limits.max_system_memory_gb:.1f}GB"
        
        # Check GPU memory
        if required_gpu_memory_gb > 0 and torch.cuda.is_available():
            if status.gpu_memory_available_gb < required_gpu_memory_gb:
                return False, f"Insufficient GPU memory: need {required_gpu_memory_gb:.1f}GB, available {status.gpu_memory_available_gb:.1f}GB"
            
            # Check if this would exceed limits
            projected_gpu_usage = status.gpu_memory_used_gb + required_gpu_memory_gb
            if projected_gpu_usage > self.limits.max_gpu_memory_gb:
                return False, f"Would exceed GPU memory limit: projected {projected_gpu_usage:.1f}GB > limit {self.limits.max_gpu_memory_gb:.1f}GB"
        
        return True, "Resources available"
    
    def start_monitoring(self):
        """Start continuous resource monitoring"""
        if self.monitoring_active:
            return
        
        self.monitoring_active = True
        self.monitor_thread = threading.Thread(target=self._monitoring_loop, daemon=True)
        self.monitor_thread.start()
        
        self.logger.info("Resource monitoring started")
    
    def stop_monitoring(self):
        """Stop resource monitoring"""
        self.monitoring_active = False
        if self.monitor_thread:
            self.monitor_thread.join(timeout=5.0)
        
        self.logger.info("Resource monitoring stopped")
    
    def _monitoring_loop(self):
        """Main monitoring loop"""
        while self.monitoring_active:
            try:
                status = self.get_current_status()
                
                # Add to history
                self.resource_history.append(status)
                
                # Keep history manageable
                if len(self.resource_history) > 1000:
                    self.resource_history = self.resource_history[-500:]
                
                # Check thresholds
                self._check_thresholds(status)
                
                # Sleep until next check
                time.sleep(self.limits.cleanup_interval_seconds)
                
            except Exception as e:
                self.logger.error(f"Monitoring loop error: {e}")
                time.sleep(5.0)
    
    def _check_thresholds(self, status: ResourceStatus):
        """Check resource thresholds and trigger alerts"""
        # System memory checks
        memory_ratio = status.system_memory_used_gb / self.limits.max_system_memory_gb
        
        if memory_ratio >= self.limits.memory_critical_threshold:
            self.memory_critical_events += 1
            self.logger.critical(f"CRITICAL: System memory usage {status.system_memory_used_gb:.1f}GB ({memory_ratio*100:.1f}%)")
            self._trigger_emergency_cleanup()
            
        elif memory_ratio >= self.limits.memory_warning_threshold:
            self.memory_warnings += 1
            self.logger.warning(f"WARNING: High system memory usage {status.system_memory_used_gb:.1f}GB ({memory_ratio*100:.1f}%)")
            self._trigger_cleanup()
        
        # GPU memory checks
        if torch.cuda.is_available():
            gpu_ratio = status.gpu_memory_used_gb / self.limits.max_gpu_memory_gb
            
            if gpu_ratio >= self.limits.memory_critical_threshold:
                self.logger.critical(f"CRITICAL: GPU memory usage {status.gpu_memory_used_gb:.1f}GB ({gpu_ratio*100:.1f}%)")
                self._trigger_gpu_cleanup()
                
            elif gpu_ratio >= self.limits.memory_warning_threshold:
                self.logger.warning(f"WARNING: High GPU memory usage {status.gpu_memory_used_gb:.1f}GB ({gpu_ratio*100:.1f}%)")
                self._trigger_gpu_cleanup()
        
        # Trigger callbacks
        for callback in self.alert_callbacks:
            try:
                callback(status)
            except Exception as e:
                self.logger.error(f"Alert callback failed: {e}")
    
    def _trigger_cleanup(self):
        """Trigger standard cleanup"""
        self.logger.info("Triggering memory cleanup")
        gc.collect()
    
    def _trigger_gpu_cleanup(self):
        """Trigger GPU memory cleanup"""
        if torch.cuda.is_available():
            self.logger.info("Triggering GPU memory cleanup")
            torch.cuda.empty_cache()
            torch.cuda.synchronize()
    
    def _trigger_emergency_cleanup(self):
        """Trigger emergency cleanup"""
        self.logger.warning("Triggering emergency cleanup")
        
        # Standard cleanup
        gc.collect()
        
        # GPU cleanup
        if torch.cuda.is_available():
            torch.cuda.empty_cache()
            torch.cuda.synchronize()
        
        # Force garbage collection multiple times
        for _ in range(3):
            gc.collect()
            time.sleep(0.1)
    
    def add_alert_callback(self, callback: Callable[[ResourceStatus], None]):
        """Add callback for resource alerts"""
        self.alert_callbacks.append(callback)
    
    def get_resource_summary(self) -> Dict[str, Any]:
        """Get resource usage summary"""
        status = self.get_current_status()
        
        return {
            "current_status": {
                "system_memory_gb": status.system_memory_used_gb,
                "system_memory_percent": status.system_memory_percent,
                "gpu_memory_gb": status.gpu_memory_used_gb,
                "gpu_memory_percent": status.gpu_memory_percent,
                "cpu_percent": status.cpu_percent
            },
            "peak_usage": {
                "peak_memory_gb": self.peak_memory_usage,
                "peak_gpu_gb": self.peak_gpu_usage
            },
            "limits": {
                "max_system_memory_gb": self.limits.max_system_memory_gb,
                "max_gpu_memory_gb": self.limits.max_gpu_memory_gb,
                "max_cpu_percent": self.limits.max_cpu_percent
            },
            "alerts": {
                "memory_warnings": self.memory_warnings,
                "critical_events": self.memory_critical_events
            },
            "monitoring_active": self.monitoring_active
        }


class ResourceManager:
    """
    Comprehensive resource management system
    
    Provides dynamic resource allocation, automatic cleanup, and
    graceful degradation for memory-constrained environments.
    """
    
    def __init__(self, limits: Optional[ResourceLimits] = None):
        self.limits = limits or self._detect_system_limits()
        self.monitor = ResourceMonitor(self.limits)
        self.logger = get_unified_logger(f"{__name__}.ResourceManager")
        
        # Resource allocation tracking
        self.allocated_resources: Dict[str, Dict[str, float]] = {}
        self.cleanup_functions: List[Callable] = []
        
        # Start monitoring
        self.monitor.start_monitoring()
        
        self.logger.info(f"Resource manager initialized with limits: "
                        f"RAM {self.limits.max_system_memory_gb:.1f}GB, "
                        f"GPU {self.limits.max_gpu_memory_gb:.1f}GB")
    
    def _detect_system_limits(self) -> ResourceLimits:
        """Automatically detect system resource limits with optimized allocation"""
        # System memory - optimized for 70-80% utilization target
        memory_info = psutil.virtual_memory()
        total_memory_gb = memory_info.total / (1024**3)
        available_memory_gb = memory_info.available / (1024**3)

        # Use 75% of available memory, cap at 25GB for stability
        max_system_memory = min(available_memory_gb * 0.75, 25.0)

        # GPU memory - RTX 3070 optimized
        max_gpu_memory = 4.0  # Conservative fallback
        if torch.cuda.is_available():
            gpu_props = torch.cuda.get_device_properties(0)
            gpu_memory_gb = gpu_props.total_memory / (1024**3)
            gpu_name = gpu_props.name

            if "RTX 3070" in gpu_name:
                # RTX 3070 specific: use 85% of 8GB = 6.8GB
                max_gpu_memory = 6.8
            else:
                # Other GPUs: use 80% of available, cap at 6GB
                max_gpu_memory = min(gpu_memory_gb * 0.8, 6.0)

        limits = ResourceLimits(
            max_system_memory_gb=max_system_memory,
            max_gpu_memory_gb=max_gpu_memory,
            max_cpu_percent=80.0,
            memory_warning_threshold=0.80,  # Less conservative
            memory_critical_threshold=0.90  # Allow higher utilization
        )

        return limits
    
    def allocate_resources(self, component_name: str, 
                          memory_gb: float = 0.0, 
                          gpu_memory_gb: float = 0.0) -> Tuple[bool, str]:
        """Allocate resources for a component"""
        # Check availability
        available, message = self.monitor.check_resource_availability(memory_gb, gpu_memory_gb)
        
        if not available:
            return False, message
        
        # Record allocation
        self.allocated_resources[component_name] = {
            "memory_gb": memory_gb,
            "gpu_memory_gb": gpu_memory_gb,
            "allocated_at": time.time()
        }
        
        self.logger.info(f"Allocated resources for {component_name}: "
                        f"RAM {memory_gb:.1f}GB, GPU {gpu_memory_gb:.1f}GB")
        
        return True, "Resources allocated successfully"
    
    def deallocate_resources(self, component_name: str):
        """Deallocate resources for a component"""
        if component_name in self.allocated_resources:
            allocation = self.allocated_resources.pop(component_name)
            self.logger.info(f"Deallocated resources for {component_name}: "
                           f"RAM {allocation['memory_gb']:.1f}GB, "
                           f"GPU {allocation['gpu_memory_gb']:.1f}GB")
        
        # Trigger cleanup
        self.cleanup_memory()
    
    def get_optimal_batch_size(self, base_batch_size: int, 
                              memory_per_sample_mb: float) -> int:
        """Calculate optimal batch size based on available memory"""
        status = self.monitor.get_current_status()
        
        # Available memory in MB
        available_memory_mb = status.system_memory_available_gb * 1024
        
        # Reserve some memory for other operations
        usable_memory_mb = available_memory_mb * 0.7
        
        # Calculate maximum batch size
        max_batch_size = int(usable_memory_mb / memory_per_sample_mb)
        
        # Use minimum of requested and maximum
        optimal_batch_size = min(base_batch_size, max_batch_size, 32)  # Cap at 32
        
        # Ensure at least 1
        optimal_batch_size = max(1, optimal_batch_size)
        
        if optimal_batch_size != base_batch_size:
            self.logger.info(f"Adjusted batch size from {base_batch_size} to {optimal_batch_size} "
                           f"based on available memory ({available_memory_mb:.0f}MB)")
        
        return optimal_batch_size
    
    def cleanup_memory(self):
        """Perform comprehensive memory cleanup"""
        self.logger.debug("Performing memory cleanup")
        
        # Run registered cleanup functions
        for cleanup_func in self.cleanup_functions:
            try:
                cleanup_func()
            except Exception as e:
                self.logger.error(f"Cleanup function failed: {e}")
        
        # Standard cleanup
        gc.collect()
        
        # GPU cleanup
        if torch.cuda.is_available():
            torch.cuda.empty_cache()
            torch.cuda.synchronize()
    
    def register_cleanup_function(self, cleanup_func: Callable):
        """Register a cleanup function"""
        self.cleanup_functions.append(cleanup_func)
    
    def get_memory_efficient_config(self, base_config: Dict[str, Any]) -> Dict[str, Any]:
        """Get memory-efficient configuration based on available resources"""
        status = self.monitor.get_current_status()
        config = base_config.copy()
        
        # Adjust based on available memory
        memory_ratio = status.system_memory_available_gb / self.limits.max_system_memory_gb
        
        if memory_ratio < 0.3:  # Very low memory
            config.update({
                "batch_size": 1,
                "num_workers": 1,
                "pin_memory": False,
                "persistent_workers": False,
                "prefetch_factor": 1
            })
            self.logger.warning("Applied very low memory configuration")
            
        elif memory_ratio < 0.5:  # Low memory
            config.update({
                "batch_size": min(config.get("batch_size", 2), 2),
                "num_workers": min(config.get("num_workers", 2), 2),
                "pin_memory": False,
                "persistent_workers": False,
                "prefetch_factor": 2
            })
            self.logger.info("Applied low memory configuration")
        
        # GPU memory adjustments
        if torch.cuda.is_available():
            gpu_ratio = status.gpu_memory_available_gb / self.limits.max_gpu_memory_gb
            
            if gpu_ratio < 0.3:
                config.update({
                    "mixed_precision": False,
                    "gradient_checkpointing": True,
                    "compile_model": False
                })
                self.logger.warning("Applied very low GPU memory configuration")
        
        return config
    
    def __enter__(self):
        """Context manager entry"""
        return self
    
    def __exit__(self, exc_type, exc_val, exc_tb):
        """Context manager exit with cleanup"""
        self.cleanup_memory()
        self.monitor.stop_monitoring()
        
        if exc_type is not None:
            self.logger.error(f"Exception during resource management: {exc_type.__name__}: {exc_val}")
        
        # Final cleanup
        for _ in range(2):
            gc.collect()
            if torch.cuda.is_available():
                torch.cuda.empty_cache()


# Global resource manager instance
_global_resource_manager: Optional[ResourceManager] = None


def get_resource_manager() -> ResourceManager:
    """Get global resource manager instance"""
    global _global_resource_manager
    
    if _global_resource_manager is None:
        _global_resource_manager = ResourceManager()
    
    return _global_resource_manager


def cleanup_global_resources():
    """Cleanup global resources"""
    global _global_resource_manager
    
    if _global_resource_manager is not None:
        _global_resource_manager.cleanup_memory()
        _global_resource_manager.monitor.stop_monitoring()
        _global_resource_manager = None
    
    # Final cleanup
    gc.collect()
    if torch.cuda.is_available():
        torch.cuda.empty_cache()


# Memory-efficient decorators
def memory_efficient(memory_limit_gb: float = 1.0):
    """Decorator for memory-efficient function execution"""
    def decorator(func):
        def wrapper(*args, **kwargs):
            resource_manager = get_resource_manager()
            
            # Check memory availability
            available, message = resource_manager.monitor.check_resource_availability(memory_limit_gb)
            
            if not available:
                raise MemoryError(f"Insufficient memory for {func.__name__}: {message}")
            
            try:
                # Cleanup before execution
                resource_manager.cleanup_memory()
                
                # Execute function
                result = func(*args, **kwargs)
                
                return result
                
            finally:
                # Cleanup after execution
                resource_manager.cleanup_memory()
        
        return wrapper
    return decorator
